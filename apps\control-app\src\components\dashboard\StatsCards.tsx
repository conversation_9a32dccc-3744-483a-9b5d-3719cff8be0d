'use client';

import React from 'react';
import { 
  BuildingOfficeIcon, 
  TruckIcon, 
  MapIcon, 
  CurrencyDollarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
} from '@heroicons/react/24/outline';
import { Card } from '@samatransport/ui';
import type { DashboardStats } from '@/services/dashboardService';

interface StatsCardsProps {
  stats: DashboardStats | null;
  loading?: boolean;
}

interface StatCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: React.ComponentType<{ className?: string }>;
  trend?: {
    value: number;
    isPositive: boolean;
    period: string;
  };
  color: 'primary' | 'secondary' | 'success' | 'warning';
  loading?: boolean;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  subtitle,
  icon: Icon,
  trend,
  color,
  loading = false,
}) => {
  const colorClasses = {
    primary: 'bg-primary-100 text-primary-600',
    secondary: 'bg-secondary-100 text-secondary-600',
    success: 'bg-success-100 text-success-600',
    warning: 'bg-warning-100 text-warning-600',
  };

  if (loading) {
    return (
      <Card className="animate-pulse">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-neutral-200 rounded-lg"></div>
          </div>
          <div className="ml-5 w-0 flex-1">
            <div className="h-4 bg-neutral-200 rounded w-24 mb-2"></div>
            <div className="h-6 bg-neutral-200 rounded w-16"></div>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className="hover:shadow-md transition-shadow duration-200">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${colorClasses[color]}`}>
            <Icon className="w-6 h-6" />
          </div>
        </div>
        <div className="ml-5 w-0 flex-1">
          <dl>
            <dt className="text-sm font-medium text-neutral-500 truncate">
              {title}
            </dt>
            <dd className="flex items-baseline">
              <div className="text-2xl font-semibold text-neutral-900">
                {typeof value === 'number' ? value.toLocaleString() : value}
              </div>
              {trend && (
                <div className={`ml-2 flex items-baseline text-sm font-semibold ${
                  trend.isPositive ? 'text-success-600' : 'text-error-600'
                }`}>
                  {trend.isPositive ? (
                    <ArrowUpIcon className="self-center flex-shrink-0 h-4 w-4" />
                  ) : (
                    <ArrowDownIcon className="self-center flex-shrink-0 h-4 w-4" />
                  )}
                  <span className="sr-only">
                    {trend.isPositive ? 'Augmentation' : 'Diminution'} de
                  </span>
                  {Math.abs(trend.value)}%
                </div>
              )}
            </dd>
            {subtitle && (
              <dd className="text-sm text-neutral-600 mt-1">
                {subtitle}
              </dd>
            )}
            {trend && (
              <dd className="text-xs text-neutral-500 mt-1">
                {trend.period}
              </dd>
            )}
          </dl>
        </div>
      </div>
    </Card>
  );
};

/**
 * Composant d'affichage des cartes de statistiques du dashboard
 */
export const StatsCards: React.FC<StatsCardsProps> = ({ stats, loading = false }) => {
  const formatCurrency = (amount: number, currency: string) => {
    if (currency === 'XOF') {
      return `${(amount / 1000).toFixed(0)}K CFA`;
    }
    return `${amount.toLocaleString()} ${currency}`;
  };

  const statsCards = [
    {
      title: 'Total Agences',
      value: stats?.agencies.total || 0,
      subtitle: `${stats?.agencies.active || 0} actives, ${stats?.agencies.inactive || 0} inactives`,
      icon: BuildingOfficeIcon,
      color: 'primary' as const,
      trend: {
        value: 12,
        isPositive: true,
        period: 'vs mois dernier',
      },
    },
    {
      title: 'Flotte Véhicules',
      value: stats?.vehicles.total || 0,
      subtitle: `${stats?.vehicles.active || 0} actifs, ${stats?.vehicles.maintenance || 0} en maintenance`,
      icon: TruckIcon,
      color: 'secondary' as const,
      trend: {
        value: 5,
        isPositive: true,
        period: 'vs mois dernier',
      },
    },
    {
      title: 'Voyages ce mois',
      value: stats?.trips.thisMonth || 0,
      subtitle: `${stats?.trips.today || 0} aujourd'hui, ${stats?.trips.thisWeek || 0} cette semaine`,
      icon: MapIcon,
      color: 'success' as const,
      trend: {
        value: 8,
        isPositive: true,
        period: 'vs mois dernier',
      },
    },
    {
      title: 'Revenus ce mois',
      value: stats ? formatCurrency(stats.revenue.thisMonth, stats.revenue.currency) : '0',
      subtitle: stats ? `${formatCurrency(stats.revenue.today, stats.revenue.currency)} aujourd'hui` : '',
      icon: CurrencyDollarIcon,
      color: 'warning' as const,
      trend: {
        value: 15,
        isPositive: true,
        period: 'vs mois dernier',
      },
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statsCards.map((card, index) => (
        <StatCard
          key={index}
          {...card}
          loading={loading}
        />
      ))}
    </div>
  );
};
