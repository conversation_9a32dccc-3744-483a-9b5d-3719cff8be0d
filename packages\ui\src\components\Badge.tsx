import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../utils/cn';

/**
 * Variantes du composant Badge
 */
const badgeVariants = cva(
  'inline-flex items-center rounded-full font-medium transition-colors duration-200',
  {
    variants: {
      variant: {
        default: 'bg-neutral-100 text-neutral-800',
        primary: 'bg-primary-100 text-primary-800',
        secondary: 'bg-secondary-100 text-secondary-800',
        success: 'bg-success-100 text-success-800',
        warning: 'bg-warning-100 text-warning-800',
        error: 'bg-error-100 text-error-800',
        outline: 'border border-neutral-300 text-neutral-700 bg-white',
      },
      size: {
        sm: 'px-2 py-0.5 text-xs',
        md: 'px-2.5 py-0.5 text-sm',
        lg: 'px-3 py-1 text-sm',
      },
      dot: {
        true: 'pl-1.5',
        false: '',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
      dot: false,
    },
  }
);

/**
 * Interface des props du composant Badge
 */
export interface BadgeProps
  extends React.HTMLAttributes<HTMLSpanElement>,
    VariantProps<typeof badgeVariants> {
  /** Contenu du badge */
  children: React.ReactNode;
  /** Afficher un point coloré */
  dot?: boolean;
  /** Couleur du point (si dot=true) */
  dotColor?: string;
  /** Icône à afficher */
  icon?: React.ReactNode;
}

/**
 * Composant Badge du Design System SAMATRANSPORT
 *
 * @example
 * <Badge variant="success">Actif</Badge>
 *
 * @example
 * <Badge variant="warning" dot>En attente</Badge>
 *
 * @example
 * <Badge variant="primary" icon={<CheckIcon />}>Validé</Badge>
 */
export const Badge = React.forwardRef<HTMLSpanElement, BadgeProps>(
  (
    {
      className,
      variant,
      size,
      dot,
      dotColor,
      icon,
      children,
      ...props
    },
    ref
  ) => {
    return (
      <span
        className={cn(badgeVariants({ variant, size, dot, className }))}
        ref={ref}
        {...props}
      >
        {/* Point coloré */}
        {dot && (
          <span
            className={cn(
              'mr-1.5 h-1.5 w-1.5 rounded-full',
              dotColor ? '' : 'bg-current'
            )}
            style={dotColor ? { backgroundColor: dotColor } : undefined}
          />
        )}

        {/* Icône */}
        {icon && (
          <span className="mr-1 flex-shrink-0">
            {React.cloneElement(icon as React.ReactElement, {
              className: cn('h-3 w-3', (icon as React.ReactElement).props?.className),
            })}
          </span>
        )}

        {children}
      </span>
    );
  }
);

Badge.displayName = 'Badge';

/**
 * Badges prédéfinis pour les statuts courants
 */
export const StatusBadge: React.FC<{
  status: 'active' | 'inactive' | 'pending' | 'success' | 'error' | 'warning';
  children?: React.ReactNode;
}> = ({ status, children }) => {
  const statusConfig = {
    active: { variant: 'success' as const, label: 'Actif', dot: true },
    inactive: { variant: 'default' as const, label: 'Inactif', dot: true },
    pending: { variant: 'warning' as const, label: 'En attente', dot: true },
    success: { variant: 'success' as const, label: 'Succès', dot: true },
    error: { variant: 'error' as const, label: 'Erreur', dot: true },
    warning: { variant: 'warning' as const, label: 'Attention', dot: true },
  };

  const config = statusConfig[status];

  return (
    <Badge variant={config.variant} dot={config.dot}>
      {children || config.label}
    </Badge>
  );
};

/**
 * Badge pour les devises
 */
export const CurrencyBadge: React.FC<{
  currency: 'XOF' | 'GNF' | 'LRD' | 'SLL' | 'EUR' | 'USD';
}> = ({ currency }) => {
  const currencyConfig = {
    XOF: { label: 'CFA', variant: 'primary' as const },
    GNF: { label: 'GNF', variant: 'secondary' as const },
    LRD: { label: 'L$', variant: 'default' as const },
    SLL: { label: 'Le', variant: 'default' as const },
    EUR: { label: '€', variant: 'primary' as const },
    USD: { label: '$', variant: 'success' as const },
  };

  const config = currencyConfig[currency];

  return (
    <Badge variant={config.variant} size="sm">
      {config.label}
    </Badge>
  );
};

export default Badge;
