import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../utils/cn';
import { useId } from '../hooks/useId';
import { ChevronDownIcon } from '@heroicons/react/24/outline';

/**
 * Variantes du composant Select
 */
const selectVariants = cva(
  'block w-full rounded-lg border transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed appearance-none bg-white',
  {
    variants: {
      variant: {
        default: 'border-neutral-300 focus:border-primary-500 focus:ring-primary-500',
        error: 'border-error-300 focus:border-error-500 focus:ring-error-500',
        success: 'border-success-300 focus:border-success-500 focus:ring-success-500',
      },
      size: {
        sm: 'px-3 py-2 text-sm',
        md: 'px-4 py-2.5 text-sm',
        lg: 'px-4 py-3 text-base',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
    },
  }
);

/**
 * Interface pour les options du Select
 */
export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

/**
 * Interface des props du composant Select
 */
export interface SelectProps
  extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'size'>,
    VariantProps<typeof selectVariants> {
  /** Label du champ */
  label?: string;
  /** Message d'erreur */
  error?: string;
  /** Message d'aide */
  helperText?: string;
  /** Options du select */
  options: SelectOption[];
  /** Placeholder */
  placeholder?: string;
  /** Indique si le champ est requis */
  required?: boolean;
}

/**
 * Composant Select du Design System SAMATRANSPORT
 *
 * @example
 * <Select
 *   label="Pays"
 *   placeholder="Sélectionnez un pays"
 *   options={[
 *     { value: 'ci', label: "Côte d'Ivoire" },
 *     { value: 'gn', label: 'Guinée' }
 *   ]}
 *   required
 * />
 */
export const Select = React.forwardRef<HTMLSelectElement, SelectProps>(
  (
    {
      className,
      variant,
      size,
      label,
      error,
      helperText,
      options,
      placeholder,
      required,
      id,
      ...props
    },
    ref
  ) => {
    const generatedId = useId('select');
    const selectId = id || generatedId;
    const hasError = Boolean(error);
    const selectVariant = hasError ? 'error' : variant;

    return (
      <div className="w-full">
        {/* Label */}
        {label && (
          <label
            htmlFor={selectId}
            className="block text-sm font-medium text-neutral-700 mb-1"
          >
            {label}
            {required && <span className="text-error-500 ml-1">*</span>}
          </label>
        )}

        {/* Select avec icône */}
        <div className="relative">
          <select
            id={selectId}
            className={cn(selectVariants({ variant: selectVariant, size, className }))}
            ref={ref}
            {...props}
          >
            {placeholder && (
              <option value="" disabled>
                {placeholder}
              </option>
            )}
            {options.map((option) => (
              <option
                key={option.value}
                value={option.value}
                disabled={option.disabled}
              >
                {option.label}
              </option>
            ))}
          </select>

          {/* Icône chevron */}
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <ChevronDownIcon className="h-5 w-5 text-neutral-400" />
          </div>
        </div>

        {/* Message d'aide ou d'erreur */}
        {(error || helperText) && (
          <p
            className={cn(
              'mt-1 text-sm',
              hasError ? 'text-error-600' : 'text-neutral-500'
            )}
          >
            {error || helperText}
          </p>
        )}
      </div>
    );
  }
);

Select.displayName = 'Select';

export default Select;
