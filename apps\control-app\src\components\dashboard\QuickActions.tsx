'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { 
  PlusIcon,
  TruckIcon,
  UsersIcon,
  MapIcon,
  ClockIcon,
  ChartBarIcon,
  WrenchScrewdriverIcon,
  BuildingOfficeIcon,
} from '@heroicons/react/24/outline';
import { <PERSON>, Button } from '@samatransport/ui';

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  color: 'primary' | 'secondary' | 'success' | 'warning';
  href?: string;
  onClick?: () => void;
  disabled?: boolean;
}

interface QuickActionsProps {
  onActionClick?: (actionId: string) => void;
}

/**
 * Composant d'actions rapides pour le dashboard
 */
export const QuickActions: React.FC<QuickActionsProps> = ({ onActionClick }) => {
  const router = useRouter();

  const handleActionClick = (action: QuickAction) => {
    if (action.disabled) return;

    if (action.onClick) {
      action.onClick();
    } else if (action.href) {
      router.push(action.href);
    }

    if (onActionClick) {
      onActionClick(action.id);
    }
  };

  const quickActions: QuickAction[] = [
    {
      id: 'add-agency',
      title: 'Nouvelle Agence',
      description: 'Créer une nouvelle agence',
      icon: BuildingOfficeIcon,
      color: 'primary',
      href: '/agencies',
    },
    {
      id: 'add-vehicle',
      title: 'Ajouter Véhicule',
      description: 'Enregistrer un nouveau véhicule',
      icon: TruckIcon,
      color: 'secondary',
      href: '/fleet',
      disabled: true, // Sera activé quand la page sera créée
    },
    {
      id: 'manage-users',
      title: 'Gérer Utilisateurs',
      description: 'Ajouter ou modifier des utilisateurs',
      icon: UsersIcon,
      color: 'success',
      href: '/users',
      disabled: true, // Sera activé quand la page sera créée
    },
    {
      id: 'create-route',
      title: 'Nouvel Itinéraire',
      description: 'Définir un nouvel itinéraire',
      icon: MapIcon,
      color: 'warning',
      href: '/routes',
      disabled: true, // Sera activé quand la page sera créée
    },
    {
      id: 'schedule-trip',
      title: 'Planifier Voyage',
      description: 'Programmer un nouveau voyage',
      icon: ClockIcon,
      color: 'primary',
      onClick: () => {
        // TODO: Ouvrir un modal de planification
        alert('Fonctionnalité en cours de développement');
      },
    },
    {
      id: 'maintenance',
      title: 'Maintenance',
      description: 'Programmer une maintenance',
      icon: WrenchScrewdriverIcon,
      color: 'warning',
      href: '/maintenance',
      disabled: true, // Sera activé quand la page sera créée
    },
    {
      id: 'reports',
      title: 'Rapports',
      description: 'Consulter les rapports',
      icon: ChartBarIcon,
      color: 'success',
      href: '/reports',
      disabled: true, // Sera activé quand la page sera créée
    },
    {
      id: 'quick-booking',
      title: 'Réservation Rapide',
      description: 'Créer une réservation',
      icon: PlusIcon,
      color: 'secondary',
      onClick: () => {
        // TODO: Ouvrir un modal de réservation rapide
        alert('Fonctionnalité en cours de développement');
      },
    },
  ];

  const getColorClasses = (color: QuickAction['color'], disabled: boolean) => {
    if (disabled) {
      return {
        button: 'bg-neutral-100 text-neutral-400 cursor-not-allowed',
        icon: 'text-neutral-400',
      };
    }

    const colorMap = {
      primary: {
        button: 'bg-primary-50 text-primary-700 hover:bg-primary-100 border-primary-200',
        icon: 'text-primary-600',
      },
      secondary: {
        button: 'bg-secondary-50 text-secondary-700 hover:bg-secondary-100 border-secondary-200',
        icon: 'text-secondary-600',
      },
      success: {
        button: 'bg-success-50 text-success-700 hover:bg-success-100 border-success-200',
        icon: 'text-success-600',
      },
      warning: {
        button: 'bg-warning-50 text-warning-700 hover:bg-warning-100 border-warning-200',
        icon: 'text-warning-600',
      },
    };

    return colorMap[color];
  };

  return (
    <Card title="Actions Rapides" description="Accès direct aux fonctionnalités principales">
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {quickActions.map((action) => {
          const colorClasses = getColorClasses(action.color, action.disabled || false);
          const IconComponent = action.icon;

          return (
            <button
              key={action.id}
              onClick={() => handleActionClick(action)}
              disabled={action.disabled}
              className={`
                p-4 rounded-lg border transition-all duration-200 text-left
                ${colorClasses.button}
                ${action.disabled ? '' : 'hover:shadow-sm transform hover:-translate-y-0.5'}
              `}
            >
              <div className="flex flex-col items-center text-center space-y-2">
                <div className={`w-8 h-8 ${colorClasses.icon}`}>
                  <IconComponent className="w-full h-full" />
                </div>
                <div>
                  <h3 className="text-sm font-medium">
                    {action.title}
                  </h3>
                  <p className="text-xs mt-1 opacity-75">
                    {action.description}
                  </p>
                </div>
              </div>
            </button>
          );
        })}
      </div>

      <div className="mt-6 pt-4 border-t border-neutral-200">
        <div className="flex items-center justify-between">
          <p className="text-sm text-neutral-600">
            Besoin d'aide ? Consultez la documentation
          </p>
          <Button variant="outline" size="sm">
            Guide d'utilisation
          </Button>
        </div>
      </div>
    </Card>
  );
};
