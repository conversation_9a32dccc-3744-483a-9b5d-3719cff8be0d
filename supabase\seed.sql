-- Seed data for SAMATRANSPORT Control Application
-- This file contains realistic test data for development and testing

-- Clear existing data (in correct order to respect foreign keys)
DELETE FROM bookings;
DELETE FROM trips;
DELETE FROM schedules;
DELETE FROM routes;
DELETE FROM vehicles;
DELETE FROM agencies;
DELETE FROM users;

-- Insert test agencies
INSERT INTO agencies (id, name, code, address, city, country, phone, email, currency, is_active, created_at, updated_at) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'Agence Abidjan Centre', 'ABI-CTR', 'Boulevard de la République, Plateau', 'Abidjan', 'CI', '+225 20 21 22 23', '<EMAIL>', 'XOF', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440002', 'Agence Bouaké', 'BKE-001', 'Avenue Félix Ho<PERSON>hou<PERSON>-Bo<PERSON>y', '<PERSON><PERSON><PERSON><PERSON>', 'CI', '+225 31 63 45 67', '<EMAIL>', 'XOF', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440003', 'Agence Yamoussoukro', 'YAM-001', 'Boulevard Mamadou Konaté', 'Yamoussoukro', 'CI', '+225 30 64 12 34', '<EMAIL>', 'XOF', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440004', 'Agence San Pedro', 'SPD-001', 'Rue du Port', 'San Pedro', 'CI', '+225 34 71 23 45', '<EMAIL>', 'XOF', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440005', 'Agence Conakry', 'CNK-001', 'Avenue de la République, Kaloum', 'Conakry', 'GN', '+224 622 123 456', '<EMAIL>', 'GNF', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440006', 'Agence Kankan', 'KNK-001', 'Route de Siguiri', 'Kankan', 'GN', '+224 621 234 567', '<EMAIL>', 'GNF', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440007', 'Agence Monrovia', 'MLW-001', 'Broad Street, Central Monrovia', 'Monrovia', 'LR', '+231 555 123 456', '<EMAIL>', 'LRD', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440008', 'Agence Freetown', 'FTN-001', 'Siaka Stevens Street', 'Freetown', 'SL', '+232 22 123 456', '<EMAIL>', 'SLL', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440009', 'Agence Korhogo', 'KRG-001', 'Avenue de la Paix', 'Korhogo', 'CI', '+225 36 86 12 34', '<EMAIL>', 'XOF', false, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440010', 'Agence Man', 'MAN-001', 'Rue de la Cascade', 'Man', 'CI', '+225 33 79 45 67', '<EMAIL>', 'XOF', true, NOW(), NOW());

-- Insert test users
INSERT INTO users (id, email, first_name, last_name, role, agency_id, is_active, created_at, updated_at) VALUES
('550e8400-e29b-41d4-a716-446655440101', '<EMAIL>', 'Amadou', 'Koné', 'ADMIN', NULL, true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440102', '<EMAIL>', 'Fatou', 'Traoré', 'MANAGER', '550e8400-e29b-41d4-a716-446655440001', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440103', '<EMAIL>', 'Kouassi', 'Yao', 'AGENT', '550e8400-e29b-41d4-a716-446655440001', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440104', '<EMAIL>', 'Mariam', 'Diabaté', 'MANAGER', '550e8400-e29b-41d4-a716-446655440002', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440105', '<EMAIL>', 'Ibrahim', 'Ouattara', 'AGENT', '550e8400-e29b-41d4-a716-446655440002', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440106', '<EMAIL>', 'Aissatou', 'Diallo', 'MANAGER', '550e8400-e29b-41d4-a716-446655440005', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440107', '<EMAIL>', 'Sekou', 'Camara', 'DRIVER', '550e8400-e29b-41d4-a716-446655440001', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440108', '<EMAIL>', 'Moussa', 'Sanogo', 'DRIVER', '550e8400-e29b-41d4-a716-446655440002', true, NOW(), NOW());

-- Insert test vehicles
INSERT INTO vehicles (id, license_plate, brand, model, year, type, capacity, status, mileage, last_maintenance, next_maintenance, agency_id, created_at, updated_at) VALUES
('550e8400-e29b-41d4-a716-446655440201', 'CI-1234-AB', 'Mercedes-Benz', 'Sprinter', 2022, 'STANDARD', 18, 'ACTIVE', 45000, '2024-01-15', '2024-04-15', '550e8400-e29b-41d4-a716-446655440001', NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440202', 'CI-5678-CD', 'Toyota', 'Hiace', 2021, 'STANDARD', 14, 'ACTIVE', 62000, '2024-01-10', '2024-04-10', '550e8400-e29b-41d4-a716-446655440001', NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440203', 'CI-9012-EF', 'Mercedes-Benz', 'Vito', 2023, 'VIP', 8, 'ACTIVE', 28000, '2024-01-20', '2024-04-20', '550e8400-e29b-41d4-a716-446655440001', NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440204', 'CI-3456-GH', 'Iveco', 'Daily', 2020, 'STANDARD', 16, 'MAINTENANCE', 78000, '2024-01-25', '2024-02-25', '550e8400-e29b-41d4-a716-446655440002', NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440205', 'CI-7890-IJ', 'Ford', 'Transit', 2022, 'STANDARD', 15, 'ACTIVE', 41000, '2024-01-12', '2024-04-12', '550e8400-e29b-41d4-a716-446655440002', NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440206', 'GN-1111-AA', 'Toyota', 'Coaster', 2021, 'STANDARD', 25, 'ACTIVE', 55000, '2024-01-18', '2024-04-18', '550e8400-e29b-41d4-a716-446655440005', NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440207', 'GN-2222-BB', 'Mercedes-Benz', 'Sprinter', 2023, 'VIP', 12, 'ACTIVE', 22000, '2024-01-22', '2024-04-22', '550e8400-e29b-41d4-a716-446655440005', NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440208', 'LR-3333-CC', 'Toyota', 'Hiace', 2020, 'STANDARD', 14, 'OUT_OF_SERVICE', 95000, '2023-12-15', '2024-03-15', '550e8400-e29b-41d4-a716-446655440007', NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440209', 'CI-4444-DD', 'Peugeot', 'Boxer', 2022, 'STANDARD', 17, 'ACTIVE', 38000, '2024-01-14', '2024-04-14', '550e8400-e29b-41d4-a716-446655440003', NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440210', 'CI-5555-EE', 'Renault', 'Master', 2021, 'STANDARD', 16, 'MAINTENANCE', 67000, '2024-01-28', '2024-02-28', '550e8400-e29b-41d4-a716-446655440004', NOW(), NOW());

-- Insert test routes
INSERT INTO routes (id, name, origin, destination, distance_km, estimated_duration_minutes, base_price, currency, is_active, created_at, updated_at) VALUES
('550e8400-e29b-41d4-a716-446655440301', 'Abidjan - Bouaké', 'Abidjan', 'Bouaké', 348, 240, 15000, 'XOF', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440302', 'Abidjan - Yamoussoukro', 'Abidjan', 'Yamoussoukro', 230, 180, 12000, 'XOF', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440303', 'Bouaké - Yamoussoukro', 'Bouaké', 'Yamoussoukro', 118, 90, 8000, 'XOF', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440304', 'Abidjan - San Pedro', 'Abidjan', 'San Pedro', 340, 300, 18000, 'XOF', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440305', 'Conakry - Kankan', 'Conakry', 'Kankan', 600, 480, 250000, 'GNF', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440306', 'Abidjan - Korhogo', 'Abidjan', 'Korhogo', 635, 420, 25000, 'XOF', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440307', 'Yamoussoukro - Man', 'Yamoussoukro', 'Man', 285, 210, 14000, 'XOF', true, NOW(), NOW());

-- Insert test schedules
INSERT INTO schedules (id, route_id, departure_time, arrival_time, days_of_week, is_active, created_at, updated_at) VALUES
('550e8400-e29b-41d4-a716-446655440401', '550e8400-e29b-41d4-a716-446655440301', '06:00:00', '10:00:00', '{1,2,3,4,5,6,7}', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440402', '550e8400-e29b-41d4-a716-446655440301', '14:30:00', '18:30:00', '{1,2,3,4,5,6,7}', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440403', '550e8400-e29b-41d4-a716-446655440302', '07:00:00', '10:00:00', '{1,2,3,4,5,6}', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440404', '550e8400-e29b-41d4-a716-446655440302', '15:00:00', '18:00:00', '{1,2,3,4,5,6}', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440405', '550e8400-e29b-41d4-a716-446655440305', '08:00:00', '16:00:00', '{2,4,6}', true, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440406', '550e8400-e29b-41d4-a716-446655440304', '09:00:00', '14:00:00', '{1,3,5,7}', true, NOW(), NOW());

-- Insert test trips (recent and upcoming)
INSERT INTO trips (id, route_id, vehicle_id, driver_id, scheduled_departure, scheduled_arrival, actual_departure, actual_arrival, status, available_seats, created_at, updated_at) VALUES
('550e8400-e29b-41d4-a716-446655440501', '550e8400-e29b-41d4-a716-446655440301', '550e8400-e29b-41d4-a716-446655440201', '550e8400-e29b-41d4-a716-446655440107', '2024-01-30 06:00:00', '2024-01-30 10:00:00', '2024-01-30 06:05:00', '2024-01-30 10:15:00', 'COMPLETED', 0, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440502', '550e8400-e29b-41d4-a716-446655440301', '550e8400-e29b-41d4-a716-446655440202', '550e8400-e29b-41d4-a716-446655440108', '2024-01-30 14:30:00', '2024-01-30 18:30:00', '2024-01-30 14:45:00', NULL, 'IN_PROGRESS', 3, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440503', '550e8400-e29b-41d4-a716-446655440302', '550e8400-e29b-41d4-a716-446655440203', '550e8400-e29b-41d4-a716-446655440107', '2024-01-31 07:00:00', '2024-01-31 10:00:00', NULL, NULL, 'SCHEDULED', 5, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440504', '550e8400-e29b-41d4-a716-446655440305', '550e8400-e29b-41d4-a716-446655440206', '550e8400-e29b-41d4-a716-446655440106', '2024-01-31 08:00:00', '2024-01-31 16:00:00', NULL, NULL, 'SCHEDULED', 18, NOW(), NOW());

-- Insert test bookings
INSERT INTO bookings (id, trip_id, passenger_name, passenger_phone, passenger_email, seat_number, price, currency, status, booking_reference, created_at, updated_at) VALUES
('550e8400-e29b-41d4-a716-446655440601', '550e8400-e29b-41d4-a716-446655440501', 'Adjoa Asante', '+225 07 12 34 56', '<EMAIL>', 1, 15000, 'XOF', 'CONFIRMED', 'SAMA-001-240130', NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440602', '550e8400-e29b-41d4-a716-446655440501', 'Kwame Nkrumah', '+225 05 98 76 54', '<EMAIL>', 2, 15000, 'XOF', 'CONFIRMED', 'SAMA-002-240130', NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440603', '550e8400-e29b-41d4-a716-446655440502', 'Fatima Touré', '+225 01 23 45 67', '<EMAIL>', 1, 15000, 'XOF', 'CONFIRMED', 'SAMA-003-240130', NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440604', '550e8400-e29b-41d4-a716-446655440503', 'Mohamed Diallo', '+225 06 87 65 43', '<EMAIL>', 1, 12000, 'XOF', 'CONFIRMED', 'SAMA-004-240131', NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440605', '550e8400-e29b-41d4-a716-446655440503', 'Aminata Keita', '+225 02 34 56 78', '<EMAIL>', 2, 12000, 'XOF', 'CONFIRMED', 'SAMA-005-240131', NOW(), NOW());

-- Update sequences to avoid conflicts
SELECT setval('agencies_id_seq', (SELECT MAX(id) FROM agencies WHERE id ~ '^[0-9]+$')::bigint) WHERE EXISTS (SELECT 1 FROM agencies WHERE id ~ '^[0-9]+$');
SELECT setval('users_id_seq', (SELECT MAX(id) FROM users WHERE id ~ '^[0-9]+$')::bigint) WHERE EXISTS (SELECT 1 FROM users WHERE id ~ '^[0-9]+$');
SELECT setval('vehicles_id_seq', (SELECT MAX(id) FROM vehicles WHERE id ~ '^[0-9]+$')::bigint) WHERE EXISTS (SELECT 1 FROM vehicles WHERE id ~ '^[0-9]+$');
SELECT setval('routes_id_seq', (SELECT MAX(id) FROM routes WHERE id ~ '^[0-9]+$')::bigint) WHERE EXISTS (SELECT 1 FROM routes WHERE id ~ '^[0-9]+$');
SELECT setval('schedules_id_seq', (SELECT MAX(id) FROM schedules WHERE id ~ '^[0-9]+$')::bigint) WHERE EXISTS (SELECT 1 FROM schedules WHERE id ~ '^[0-9]+$');
SELECT setval('trips_id_seq', (SELECT MAX(id) FROM trips WHERE id ~ '^[0-9]+$')::bigint) WHERE EXISTS (SELECT 1 FROM trips WHERE id ~ '^[0-9]+$');
SELECT setval('bookings_id_seq', (SELECT MAX(id) FROM bookings WHERE id ~ '^[0-9]+$')::bigint) WHERE EXISTS (SELECT 1 FROM bookings WHERE id ~ '^[0-9]+$');
