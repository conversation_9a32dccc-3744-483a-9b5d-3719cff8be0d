import { useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import { createClientSupabase } from '@/lib/supabase';
import type { AuthUser } from '@/lib/supabase';
import type { User } from '@supabase/supabase-js';

/**
 * Hook pour la gestion de l'authentification
 */
export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(false); // Start with false to prevent hydration mismatch
  const supabase = createClientSupabase();
  const router = useRouter();

  useEffect(() => {
    // Récupérer la session initiale
    const getSession = async () => {
      setLoading(true); // Set loading to true when starting async operation
      const { data: { session } } = await supabase.auth.getSession();
      setUser(session?.user ?? null);
      setLoading(false);
    };

    getSession();

    // Écouter les changements d'authentification
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setUser(session?.user ?? null);
        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, [supabase.auth]);

  const signIn = async (email: string, password: string) => {
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      return { error };
    } catch (error) {
      return { error };
    }
  };

  const signOut = async () => {
    try {
      await supabase.auth.signOut();
      router.push('/auth/login');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      });
      return { error };
    } catch (error) {
      return { error };
    }
  };

  return {
    user,
    loading,
    signIn,
    signOut,
    resetPassword,
    isAuthenticated: !!user,
  };
}
