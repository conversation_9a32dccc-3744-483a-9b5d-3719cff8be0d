import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../utils/cn';
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  InformationCircleIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';

/**
 * Variantes du composant Alert
 */
const alertVariants = cva(
  'rounded-lg p-4 border transition-all duration-200',
  {
    variants: {
      variant: {
        info: 'bg-primary-50 border-primary-200 text-primary-800',
        success: 'bg-success-50 border-success-200 text-success-800',
        warning: 'bg-warning-50 border-warning-200 text-warning-800',
        error: 'bg-error-50 border-error-200 text-error-800',
      },
      size: {
        sm: 'text-sm',
        md: 'text-base',
      },
    },
    defaultVariants: {
      variant: 'info',
      size: 'md',
    },
  }
);

/**
 * Interface des props du composant Alert
 */
export interface AlertProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof alertVariants> {
  /** Titre de l'alerte */
  title?: string;
  /** Contenu de l'alerte */
  children: React.ReactNode;
  /** Afficher l'icône */
  showIcon?: boolean;
  /** Icône personnalisée */
  icon?: React.ReactNode;
  /** Fonction appelée lors de la fermeture */
  onClose?: () => void;
  /** Masquer le bouton de fermeture */
  hideCloseButton?: boolean;
}

/**
 * Icônes par défaut pour chaque variante
 */
const defaultIcons = {
  info: InformationCircleIcon,
  success: CheckCircleIcon,
  warning: ExclamationTriangleIcon,
  error: XCircleIcon,
};

/**
 * Composant Alert du Design System SAMATRANSPORT
 *
 * @example
 * <Alert variant="success" title="Succès">
 *   L'agence a été créée avec succès.
 * </Alert>
 *
 * @example
 * <Alert variant="error" onClose={() => setShowAlert(false)}>
 *   Une erreur est survenue lors de la sauvegarde.
 * </Alert>
 */
export const Alert = React.forwardRef<HTMLDivElement, AlertProps>(
  (
    {
      className,
      variant,
      size,
      title,
      children,
      showIcon = true,
      icon,
      onClose,
      hideCloseButton = false,
      ...props
    },
    ref
  ) => {
    const IconComponent = icon || (variant && defaultIcons[variant]);
    const canClose = onClose && !hideCloseButton;

    return (
      <div
        className={cn(alertVariants({ variant, size, className }))}
        ref={ref}
        role="alert"
        {...props}
      >
        <div className="flex">
          {/* Icône */}
          {showIcon && IconComponent && (
            <div className="flex-shrink-0">
              {React.isValidElement(icon) ? (
                icon
              ) : (
                <IconComponent className="h-5 w-5" aria-hidden="true" />
              )}
            </div>
          )}

          {/* Contenu */}
          <div className={cn('flex-1', showIcon && IconComponent && 'ml-3')}>
            {title && (
              <h3 className="text-sm font-medium mb-1">
                {title}
              </h3>
            )}
            <div className={cn(title ? 'text-sm' : '')}>
              {children}
            </div>
          </div>

          {/* Bouton de fermeture */}
          {canClose && (
            <div className="ml-auto pl-3">
              <div className="-mx-1.5 -my-1.5">
                <button
                  type="button"
                  className={cn(
                    'inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors',
                    variant === 'info' && 'text-primary-500 hover:bg-primary-100 focus:ring-primary-600',
                    variant === 'success' && 'text-success-500 hover:bg-success-100 focus:ring-success-600',
                    variant === 'warning' && 'text-warning-500 hover:bg-warning-100 focus:ring-warning-600',
                    variant === 'error' && 'text-error-500 hover:bg-error-100 focus:ring-error-600'
                  )}
                  onClick={onClose}
                >
                  <span className="sr-only">Fermer</span>
                  <XMarkIcon className="h-5 w-5" aria-hidden="true" />
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }
);

Alert.displayName = 'Alert';

/**
 * Composants d'alerte prédéfinis
 */
export const InfoAlert: React.FC<Omit<AlertProps, 'variant'>> = (props) => (
  <Alert variant="info" {...props} />
);

export const SuccessAlert: React.FC<Omit<AlertProps, 'variant'>> = (props) => (
  <Alert variant="success" {...props} />
);

export const WarningAlert: React.FC<Omit<AlertProps, 'variant'>> = (props) => (
  <Alert variant="warning" {...props} />
);

export const ErrorAlert: React.FC<Omit<AlertProps, 'variant'>> = (props) => (
  <Alert variant="error" {...props} />
);

export default Alert;
