'use client';

import React from 'react';
import { 
  TicketIcon,
  WrenchScrewdriverIcon,
  TruckIcon,
  UserPlusIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';
import { Card, Badge } from '@samatransport/ui';
import type { RecentActivity } from '@/services/dashboardService';

interface RecentActivitiesProps {
  activities: RecentActivity[];
  loading?: boolean;
}

const getActivityIcon = (type: RecentActivity['type']) => {
  switch (type) {
    case 'booking':
      return TicketIcon;
    case 'maintenance':
      return WrenchScrewdriverIcon;
    case 'trip':
      return TruckIcon;
    case 'user':
      return UserPlusIcon;
    default:
      return ClockIcon;
  }
};

const getActivityColor = (type: RecentActivity['type']) => {
  switch (type) {
    case 'booking':
      return 'primary';
    case 'maintenance':
      return 'warning';
    case 'trip':
      return 'success';
    case 'user':
      return 'secondary';
    default:
      return 'default';
  }
};

const getTypeLabel = (type: RecentActivity['type']) => {
  switch (type) {
    case 'booking':
      return 'Réservation';
    case 'maintenance':
      return 'Maintenance';
    case 'trip':
      return 'Voyage';
    case 'user':
      return 'Utilisateur';
    default:
      return type;
  }
};

const formatTimeAgo = (timestamp: string) => {
  const now = new Date();
  const activityTime = new Date(timestamp);
  const diffInMinutes = Math.floor((now.getTime() - activityTime.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) {
    return 'À l\'instant';
  } else if (diffInMinutes < 60) {
    return `Il y a ${diffInMinutes} min`;
  } else if (diffInMinutes < 1440) {
    const hours = Math.floor(diffInMinutes / 60);
    return `Il y a ${hours}h`;
  } else {
    const days = Math.floor(diffInMinutes / 1440);
    return `Il y a ${days}j`;
  }
};

/**
 * Composant d'affichage des activités récentes du dashboard
 */
export const RecentActivities: React.FC<RecentActivitiesProps> = ({ 
  activities, 
  loading = false 
}) => {
  const recentActivities = activities.slice(0, 8); // Afficher les 8 dernières activités

  if (loading) {
    return (
      <Card title="Activités récentes" className="h-96">
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="animate-pulse flex items-start space-x-3">
              <div className="w-8 h-8 bg-neutral-200 rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-neutral-200 rounded w-3/4"></div>
                <div className="h-3 bg-neutral-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </Card>
    );
  }

  return (
    <Card 
      title="Activités récentes" 
      description="Dernières actions dans le système"
      className="h-96"
    >
      <div className="space-y-4 max-h-80 overflow-y-auto">
        {recentActivities.length === 0 ? (
          <div className="text-center py-8">
            <ClockIcon className="mx-auto h-12 w-12 text-neutral-400" />
            <h3 className="mt-2 text-sm font-medium text-neutral-900">
              Aucune activité récente
            </h3>
            <p className="mt-1 text-sm text-neutral-500">
              Les activités apparaîtront ici une fois qu'elles auront lieu.
            </p>
          </div>
        ) : (
          <div className="flow-root">
            <ul className="-mb-8">
              {recentActivities.map((activity, index) => {
                const IconComponent = getActivityIcon(activity.type);
                const activityColor = getActivityColor(activity.type);
                const isLast = index === recentActivities.length - 1;

                return (
                  <li key={activity.id}>
                    <div className="relative pb-8">
                      {!isLast && (
                        <span
                          className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-neutral-200"
                          aria-hidden="true"
                        />
                      )}
                      <div className="relative flex space-x-3">
                        <div>
                          <span className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${
                            activityColor === 'primary' ? 'bg-primary-500' :
                            activityColor === 'warning' ? 'bg-warning-500' :
                            activityColor === 'success' ? 'bg-success-500' :
                            activityColor === 'secondary' ? 'bg-secondary-500' :
                            'bg-neutral-500'
                          }`}>
                            <IconComponent className="h-4 w-4 text-white" aria-hidden="true" />
                          </span>
                        </div>
                        <div className="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                          <div className="min-w-0 flex-1">
                            <p className="text-sm font-medium text-neutral-900">
                              {activity.title}
                            </p>
                            <p className="text-sm text-neutral-600 mt-0.5">
                              {activity.description}
                            </p>
                            <div className="flex items-center space-x-2 mt-2">
                              <Badge 
                                variant={activityColor} 
                                size="sm"
                              >
                                {getTypeLabel(activity.type)}
                              </Badge>
                              {activity.user && (
                                <span className="text-xs text-neutral-500">
                                  par {activity.user}
                                </span>
                              )}
                            </div>
                          </div>
                          <div className="whitespace-nowrap text-right text-xs text-neutral-500">
                            {formatTimeAgo(activity.timestamp)}
                          </div>
                        </div>
                      </div>
                    </div>
                  </li>
                );
              })}
            </ul>
          </div>
        )}
      </div>

      {activities.length > 8 && (
        <div className="mt-4 pt-4 border-t border-neutral-200">
          <button className="text-sm text-primary-600 hover:text-primary-700 font-medium">
            Voir toutes les activités
          </button>
        </div>
      )}
    </Card>
  );
};
