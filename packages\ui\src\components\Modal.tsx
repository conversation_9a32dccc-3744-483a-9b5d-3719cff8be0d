import React, { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { cn } from '../utils/cn';
import { Button } from './Button';

/**
 * Interface des props du composant Modal
 */
export interface ModalProps {
  /** État d'ouverture du modal */
  isOpen: boolean;
  /** Fonction appelée lors de la fermeture */
  onClose: () => void;
  /** Titre du modal */
  title?: string;
  /** Description du modal */
  description?: string;
  /** Contenu du modal */
  children: React.ReactNode;
  /** Taille du modal */
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  /** Actions du footer */
  footer?: React.ReactNode;
  /** Masquer le bouton de fermeture */
  hideCloseButton?: boolean;
  /** Empêcher la fermeture en cliquant à l'extérieur */
  preventClose?: boolean;
  /** Classes CSS personnalisées */
  className?: string;
}

/**
 * Tailles du modal
 */
const modalSizes = {
  sm: 'max-w-md',
  md: 'max-w-lg',
  lg: 'max-w-2xl',
  xl: 'max-w-4xl',
  full: 'max-w-7xl',
};

/**
 * Composant Modal du Design System SAMATRANSPORT
 *
 * @example
 * <Modal
 *   isOpen={isOpen}
 *   onClose={() => setIsOpen(false)}
 *   title="Créer une agence"
 *   size="lg"
 * >
 *   <p>Contenu du modal</p>
 * </Modal>
 */
export const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  title,
  description,
  children,
  size = 'md',
  footer,
  hideCloseButton = false,
  preventClose = false,
  className,
}) => {
  const handleClose = () => {
    if (!preventClose) {
      onClose();
    }
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={handleClose}>
        {/* Overlay */}
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/25 backdrop-blur-sm" />
        </Transition.Child>

        {/* Container */}
        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel
                className={cn(
                  'w-full transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all',
                  modalSizes[size],
                  className
                )}
              >
                {/* Header */}
                {(title || description || !hideCloseButton) && (
                  <div className="flex items-start justify-between p-6 border-b border-neutral-200">
                    <div className="flex-1 min-w-0">
                      {title && (
                        <Dialog.Title
                          as="h3"
                          className="text-lg font-semibold text-neutral-900"
                        >
                          {title}
                        </Dialog.Title>
                      )}
                      {description && (
                        <Dialog.Description className="mt-1 text-sm text-neutral-600">
                          {description}
                        </Dialog.Description>
                      )}
                    </div>

                    {/* Bouton de fermeture */}
                    {!hideCloseButton && (
                      <button
                        type="button"
                        className="ml-4 rounded-md bg-white text-neutral-400 hover:text-neutral-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                        onClick={handleClose}
                      >
                        <span className="sr-only">Fermer</span>
                        <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                      </button>
                    )}
                  </div>
                )}

                {/* Content */}
                <div className="p-6">
                  {children}
                </div>

                {/* Footer */}
                {footer && (
                  <div className="flex items-center justify-end gap-3 px-6 py-4 bg-neutral-50 border-t border-neutral-200">
                    {footer}
                  </div>
                )}
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};

/**
 * Hook pour gérer l'état d'un modal
 */
export const useModal = (initialState = false) => {
  const [isOpen, setIsOpen] = React.useState(initialState);

  const openModal = React.useCallback(() => setIsOpen(true), []);
  const closeModal = React.useCallback(() => setIsOpen(false), []);
  const toggleModal = React.useCallback(() => setIsOpen(prev => !prev), []);

  return {
    isOpen,
    openModal,
    closeModal,
    toggleModal,
  };
};

export default Modal;
