'use client';

import { useDashboard } from '@/hooks/useDashboard';
import { StatsCards } from '@/components/dashboard/StatsCards';
import { AlertsPanel } from '@/components/dashboard/AlertsPanel';
import { RecentActivities } from '@/components/dashboard/RecentActivities';
import { QuickActions } from '@/components/dashboard/QuickActions';
import { ErrorAlert } from '@samatransport/ui';
import { AdminLayout } from '@/components/layout';

export default function ControlDashboard() {
  const {
    stats,
    alerts,
    activities,
    loading,
    error,
    markAlertAsRead,
    refresh,
  } = useDashboard();

  const handleActionClick = (actionId: string) => {
    console.log(`Action cliquée: ${actionId}`);
    // Ici on pourrait ajouter des analytics ou des logs
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* En-tête */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-neutral-900">
              Tableau de Bord - Centre de Commande
            </h1>
            <p className="text-neutral-600">
              Vue d'ensemble de vos opérations SAMATRANSPORT
            </p>
          </div>
          <button
            onClick={refresh}
            disabled={loading}
            className="text-sm text-primary-600 hover:text-primary-700 disabled:opacity-50"
          >
            {loading ? 'Actualisation...' : 'Actualiser'}
          </button>
        </div>

        {/* Affichage des erreurs */}
        {error && (
          <ErrorAlert onClose={() => window.location.reload()}>
            {error}
          </ErrorAlert>
        )}

        {/* Métriques principales */}
        <StatsCards stats={stats} loading={loading} />

        {/* Actions rapides */}
        <QuickActions onActionClick={handleActionClick} />

        {/* Alertes et activités récentes */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <AlertsPanel
            alerts={alerts}
            onMarkAsRead={markAlertAsRead}
            loading={loading}
          />
          <RecentActivities
            activities={activities}
            loading={loading}
          />
        </div>
      </div>
    </AdminLayout>
  );
}
