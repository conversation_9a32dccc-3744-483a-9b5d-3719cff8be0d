import React from 'react';
import { cn } from '../utils/cn';

/**
 * Interface pour les colonnes du tableau
 */
export interface TableColumn<T = any> {
  /** Clé unique de la colonne */
  key: string;
  /** Titre de la colonne */
  title: string;
  /** Fonction de rendu personnalisé */
  render?: (value: any, record: T, index: number) => React.ReactNode;
  /** Largeur de la colonne */
  width?: string | number;
  /** Alignement du contenu */
  align?: 'left' | 'center' | 'right';
  /** Colonne triable */
  sortable?: boolean;
  /** Classe CSS personnalisée */
  className?: string;
}

/**
 * Interface des props du composant Table
 */
export interface TableProps<T = any> {
  /** Colonnes du tableau */
  columns: TableColumn<T>[];
  /** Données à afficher */
  data: T[];
  /** État de chargement */
  loading?: boolean;
  /** Message quand aucune donnée */
  emptyMessage?: string;
  /** Fonction appelée lors du clic sur une ligne */
  onRowClick?: (record: T, index: number) => void;
  /** Classe CSS pour les lignes */
  rowClassName?: string | ((record: T, index: number) => string);
  /** Afficher les bordures */
  bordered?: boolean;
  /** Afficher les lignes alternées */
  striped?: boolean;
  /** Taille du tableau */
  size?: 'sm' | 'md' | 'lg';
  /** Classe CSS personnalisée */
  className?: string;
}

/**
 * Composant Table du Design System SAMATRANSPORT
 *
 * @example
 * <Table
 *   columns={[
 *     { key: 'name', title: 'Nom' },
 *     { key: 'status', title: 'Statut', render: (value) => <Badge>{value}</Badge> }
 *   ]}
 *   data={agencies}
 *   onRowClick={(agency) => console.log(agency)}
 * />
 */
export const Table = <T extends Record<string, any>>({
  columns,
  data,
  loading = false,
  emptyMessage = 'Aucune donnée disponible',
  onRowClick,
  rowClassName,
  bordered = false,
  striped = false,
  size = 'md',
  className,
}: TableProps<T>) => {
  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-sm',
    lg: 'text-base',
  };

  const cellPadding = {
    sm: 'px-3 py-2',
    md: 'px-4 py-3',
    lg: 'px-6 py-4',
  };

  const getRowClassName = (record: T, index: number) => {
    let classes = 'transition-colors duration-150';
    
    if (onRowClick) {
      classes += ' cursor-pointer hover:bg-neutral-50';
    }
    
    if (striped && index % 2 === 1) {
      classes += ' bg-neutral-25';
    }

    if (typeof rowClassName === 'function') {
      classes += ` ${rowClassName(record, index)}`;
    } else if (rowClassName) {
      classes += ` ${rowClassName}`;
    }

    return classes;
  };

  const getCellValue = (record: T, column: TableColumn<T>, index: number) => {
    const value = record[column.key];
    
    if (column.render) {
      return column.render(value, record, index);
    }
    
    return value;
  };

  const getAlignmentClass = (align?: string) => {
    switch (align) {
      case 'center':
        return 'text-center';
      case 'right':
        return 'text-right';
      default:
        return 'text-left';
    }
  };

  if (loading) {
    return (
      <div className="w-full">
        <div className="animate-pulse">
          <div className="h-10 bg-neutral-200 rounded mb-2"></div>
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-12 bg-neutral-100 rounded mb-1"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={cn('w-full overflow-hidden', className)}>
      <div className="overflow-x-auto">
        <table className={cn(
          'min-w-full divide-y divide-neutral-200',
          sizeClasses[size],
          bordered && 'border border-neutral-200 rounded-lg'
        )}>
          {/* En-tête */}
          <thead className="bg-neutral-50">
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key}
                  className={cn(
                    cellPadding[size],
                    'font-medium text-neutral-900 tracking-wider',
                    getAlignmentClass(column.align),
                    column.className
                  )}
                  style={column.width ? { width: column.width } : undefined}
                >
                  {column.title}
                </th>
              ))}
            </tr>
          </thead>

          {/* Corps du tableau */}
          <tbody className="bg-white divide-y divide-neutral-200">
            {data.length === 0 ? (
              <tr>
                <td
                  colSpan={columns.length}
                  className={cn(
                    cellPadding[size],
                    'text-center text-neutral-500'
                  )}
                >
                  {emptyMessage}
                </td>
              </tr>
            ) : (
              data.map((record, index) => (
                <tr
                  key={index}
                  className={getRowClassName(record, index)}
                  onClick={onRowClick ? () => onRowClick(record, index) : undefined}
                >
                  {columns.map((column) => (
                    <td
                      key={column.key}
                      className={cn(
                        cellPadding[size],
                        'text-neutral-900',
                        getAlignmentClass(column.align),
                        column.className
                      )}
                    >
                      {getCellValue(record, column, index)}
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Table;
