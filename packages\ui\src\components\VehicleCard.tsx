import React from 'react';
import { cn } from '../utils/cn';
import { Card } from './Card';
import { Badge, StatusBadge } from './Badge';
import {
  TruckIcon,
  UserGroupIcon,
  CalendarIcon,
  WrenchScrewdriverIcon,
} from '@heroicons/react/24/outline';

/**
 * Interface pour les données d'un véhicule
 */
export interface Vehicle {
  id: string;
  license_plate: string;
  brand: string;
  model: string;
  year: number;
  type: 'STANDARD' | 'VIP';
  capacity: number;
  status: 'ACTIVE' | 'MAINTENANCE' | 'OUT_OF_SERVICE';
  mileage?: number;
  last_maintenance?: string;
  next_maintenance?: string;
  agency_name?: string;
}

/**
 * Interface des props du composant VehicleCard
 */
export interface VehicleCardProps {
  /** Données du véhicule */
  vehicle: Vehicle;
  /** Fonction appelée lors du clic sur éditer */
  onEdit?: (vehicle: Vehicle) => void;
  /** Fonction appelée lors du clic sur voir */
  onView?: (vehicle: Vehicle) => void;
  /** Fonction appelée lors du clic sur supprimer */
  onDelete?: (vehicle: Vehicle) => void;
  /** Fonction appelée lors du changement de statut */
  onStatusChange?: (vehicle: Vehicle, newStatus: Vehicle['status']) => void;
  /** Affichage compact */
  compact?: boolean;
  /** Classe CSS personnalisée */
  className?: string;
}

/**
 * Composant VehicleCard du Design System SAMATRANSPORT
 *
 * @example
 * <VehicleCard
 *   vehicle={vehicle}
 *   onEdit={(v) => setEditingVehicle(v)}
 *   onView={(v) => setViewingVehicle(v)}
 * />
 */
export const VehicleCard: React.FC<VehicleCardProps> = ({
  vehicle,
  onEdit,
  onView,
  onDelete,
  onStatusChange,
  compact = false,
  className,
}) => {
  const getStatusVariant = (status: Vehicle['status']) => {
    switch (status) {
      case 'ACTIVE':
        return 'active';
      case 'MAINTENANCE':
        return 'warning';
      case 'OUT_OF_SERVICE':
        return 'error';
      default:
        return 'inactive';
    }
  };

  const getStatusLabel = (status: Vehicle['status']) => {
    switch (status) {
      case 'ACTIVE':
        return 'Actif';
      case 'MAINTENANCE':
        return 'Maintenance';
      case 'OUT_OF_SERVICE':
        return 'Hors service';
      default:
        return status;
    }
  };

  const getTypeLabel = (type: Vehicle['type']) => {
    return type === 'VIP' ? 'VIP' : 'Standard';
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  const formatMileage = (mileage?: number) => {
    if (!mileage) return 'N/A';
    return `${mileage.toLocaleString()} km`;
  };

  return (
    <Card
      className={cn('hover:shadow-md transition-shadow duration-200', className)}
      hover
    >
      {/* En-tête */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
              <TruckIcon className="w-6 h-6 text-primary-600" />
            </div>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-neutral-900">
              {vehicle.license_plate}
            </h3>
            <p className="text-sm text-neutral-600">
              {vehicle.brand} {vehicle.model} ({vehicle.year})
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Badge variant={vehicle.type === 'VIP' ? 'primary' : 'default'} size="sm">
            {getTypeLabel(vehicle.type)}
          </Badge>
          <StatusBadge status={getStatusVariant(vehicle.status)}>
            {getStatusLabel(vehicle.status)}
          </StatusBadge>
        </div>
      </div>

      {/* Informations principales */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="flex items-center space-x-2">
          <UserGroupIcon className="w-4 h-4 text-neutral-400" />
          <span className="text-sm text-neutral-600">
            {vehicle.capacity} places
          </span>
        </div>

        {vehicle.agency_name && (
          <div className="text-sm text-neutral-600">
            <span className="font-medium">Agence:</span> {vehicle.agency_name}
          </div>
        )}

        {vehicle.mileage && (
          <div className="text-sm text-neutral-600">
            <span className="font-medium">Kilométrage:</span> {formatMileage(vehicle.mileage)}
          </div>
        )}
      </div>

      {/* Informations de maintenance */}
      {!compact && (vehicle.last_maintenance || vehicle.next_maintenance) && (
        <div className="border-t border-neutral-200 pt-4 mb-4">
          <div className="flex items-center space-x-2 mb-2">
            <WrenchScrewdriverIcon className="w-4 h-4 text-neutral-400" />
            <span className="text-sm font-medium text-neutral-700">Maintenance</span>
          </div>
          
          <div className="grid grid-cols-1 gap-2 text-sm text-neutral-600">
            {vehicle.last_maintenance && (
              <div className="flex items-center space-x-2">
                <CalendarIcon className="w-3 h-3 text-neutral-400" />
                <span>Dernière: {formatDate(vehicle.last_maintenance)}</span>
              </div>
            )}
            {vehicle.next_maintenance && (
              <div className="flex items-center space-x-2">
                <CalendarIcon className="w-3 h-3 text-neutral-400" />
                <span>Prochaine: {formatDate(vehicle.next_maintenance)}</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Actions */}
      {(onEdit || onView || onDelete) && (
        <div className="flex items-center justify-end space-x-2 pt-4 border-t border-neutral-200">
          {onView && (
            <button
              onClick={() => onView(vehicle)}
              className="text-sm text-neutral-600 hover:text-primary-600 transition-colors"
            >
              Voir
            </button>
          )}
          {onEdit && (
            <button
              onClick={() => onEdit(vehicle)}
              className="text-sm text-primary-600 hover:text-primary-700 transition-colors"
            >
              Modifier
            </button>
          )}
          {onDelete && (
            <button
              onClick={() => onDelete(vehicle)}
              className="text-sm text-error-600 hover:text-error-700 transition-colors"
            >
              Supprimer
            </button>
          )}
        </div>
      )}
    </Card>
  );
};

export default VehicleCard;
