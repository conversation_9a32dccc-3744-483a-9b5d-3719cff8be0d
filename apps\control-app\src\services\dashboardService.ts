import { createClientSupabase } from '@/lib/supabase';

/**
 * Interface pour les statistiques du dashboard
 */
export interface DashboardStats {
  agencies: {
    total: number;
    active: number;
    inactive: number;
  };
  vehicles: {
    total: number;
    active: number;
    maintenance: number;
    outOfService: number;
  };
  trips: {
    today: number;
    thisWeek: number;
    thisMonth: number;
  };
  revenue: {
    today: number;
    thisWeek: number;
    thisMonth: number;
    currency: string;
  };
}

/**
 * Interface pour les alertes du dashboard
 */
export interface DashboardAlert {
  id: string;
  type: 'maintenance' | 'trip' | 'system' | 'revenue';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  createdAt: string;
  isRead: boolean;
}

/**
 * Interface pour les activités récentes
 */
export interface RecentActivity {
  id: string;
  type: 'booking' | 'maintenance' | 'trip' | 'user';
  title: string;
  description: string;
  timestamp: string;
  user?: string;
  metadata?: Record<string, any>;
}

/**
 * Service pour les données du dashboard
 */
export class DashboardService {
  private supabase = createClientSupabase();

  /**
   * Récupère les statistiques principales du dashboard
   */
  async getDashboardStats(): Promise<DashboardStats> {
    try {
      // Statistiques des agences
      const { data: agenciesData, error: agenciesError } = await this.supabase
        .from('agencies')
        .select('is_active');

      if (agenciesError) throw agenciesError;

      const agenciesStats = {
        total: agenciesData?.length || 0,
        active: agenciesData?.filter(a => a.is_active).length || 0,
        inactive: agenciesData?.filter(a => !a.is_active).length || 0,
      };

      // Statistiques des véhicules
      const { data: vehiclesData, error: vehiclesError } = await this.supabase
        .from('vehicles')
        .select('status');

      if (vehiclesError) throw vehiclesError;

      const vehiclesStats = {
        total: vehiclesData?.length || 0,
        active: vehiclesData?.filter(v => v.status === 'ACTIVE').length || 0,
        maintenance: vehiclesData?.filter(v => v.status === 'MAINTENANCE').length || 0,
        outOfService: vehiclesData?.filter(v => v.status === 'OUT_OF_SERVICE').length || 0,
      };

      // Statistiques des voyages (simulées pour l'instant)
      const tripsStats = {
        today: 12,
        thisWeek: 84,
        thisMonth: 356,
      };

      // Statistiques de revenus (simulées pour l'instant)
      const revenueStats = {
        today: 2450000,
        thisWeek: 18750000,
        thisMonth: 89250000,
        currency: 'XOF',
      };

      return {
        agencies: agenciesStats,
        vehicles: vehiclesStats,
        trips: tripsStats,
        revenue: revenueStats,
      };
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      throw error;
    }
  }

  /**
   * Récupère les alertes du dashboard
   */
  async getDashboardAlerts(): Promise<DashboardAlert[]> {
    // Pour l'instant, retournons des données simulées
    // TODO: Implémenter avec une vraie table d'alertes
    return [
      {
        id: '1',
        type: 'maintenance',
        severity: 'high',
        title: 'Maintenance urgente',
        message: '3 véhicules nécessitent une maintenance urgente',
        createdAt: new Date().toISOString(),
        isRead: false,
      },
      {
        id: '2',
        type: 'trip',
        severity: 'medium',
        title: 'Retard signalé',
        message: 'Le voyage Abidjan-Bouaké de 14h30 a 45 minutes de retard',
        createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        isRead: false,
      },
      {
        id: '3',
        type: 'revenue',
        severity: 'low',
        title: 'Objectif atteint',
        message: 'Objectif de revenus mensuel atteint à 95%',
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        isRead: true,
      },
    ];
  }

  /**
   * Récupère les activités récentes
   */
  async getRecentActivities(): Promise<RecentActivity[]> {
    // Pour l'instant, retournons des données simulées
    // TODO: Implémenter avec un système d'audit logs
    return [
      {
        id: '1',
        type: 'booking',
        title: 'Nouvelle réservation',
        description: 'Réservation pour Abidjan-Yamoussoukro',
        timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
        user: 'Agent Kouassi',
      },
      {
        id: '2',
        type: 'maintenance',
        title: 'Maintenance terminée',
        description: 'Véhicule CI-1234-AB remis en service',
        timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
        user: 'Mécanicien Diabaté',
      },
      {
        id: '3',
        type: 'trip',
        title: 'Voyage démarré',
        description: 'Départ Abidjan-Bouaké 14h30',
        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        user: 'Chauffeur Traoré',
      },
      {
        id: '4',
        type: 'user',
        title: 'Nouvel utilisateur',
        description: 'Agent créé pour agence de San Pedro',
        timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
        user: 'Admin Koffi',
      },
    ];
  }

  /**
   * Marque une alerte comme lue
   */
  async markAlertAsRead(alertId: string): Promise<void> {
    // TODO: Implémenter avec la base de données
    console.log(`Alerte ${alertId} marquée comme lue`);
  }

  /**
   * Récupère les données pour les graphiques
   */
  async getChartData(type: 'revenue' | 'trips' | 'bookings', period: 'week' | 'month' | 'year') {
    // Données simulées pour les graphiques
    const baseData = {
      revenue: {
        week: [
          { date: '2024-01-01', value: 2500000 },
          { date: '2024-01-02', value: 3200000 },
          { date: '2024-01-03', value: 2800000 },
          { date: '2024-01-04', value: 3500000 },
          { date: '2024-01-05', value: 4100000 },
          { date: '2024-01-06', value: 3800000 },
          { date: '2024-01-07', value: 2900000 },
        ],
        month: Array.from({ length: 30 }, (_, i) => ({
          date: `2024-01-${String(i + 1).padStart(2, '0')}`,
          value: Math.floor(Math.random() * 2000000) + 2000000,
        })),
      },
      trips: {
        week: [
          { date: '2024-01-01', value: 15 },
          { date: '2024-01-02', value: 18 },
          { date: '2024-01-03', value: 12 },
          { date: '2024-01-04', value: 22 },
          { date: '2024-01-05', value: 25 },
          { date: '2024-01-06', value: 20 },
          { date: '2024-01-07', value: 16 },
        ],
      },
    };

    return baseData[type]?.[period] || [];
  }
}

// Instance singleton du service
export const dashboardService = new DashboardService();
