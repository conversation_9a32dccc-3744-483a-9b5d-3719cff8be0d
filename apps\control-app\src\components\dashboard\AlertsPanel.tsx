'use client';

import React from 'react';
import { 
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon,
  BellIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import { Card, Badge } from '@samatransport/ui';
import type { DashboardAlert } from '@/services/dashboardService';

interface AlertsPanelProps {
  alerts: DashboardAlert[];
  onMarkAsRead?: (alertId: string) => void;
  loading?: boolean;
}

const getAlertIcon = (type: DashboardAlert['type']) => {
  switch (type) {
    case 'maintenance':
      return ExclamationTriangleIcon;
    case 'trip':
      return InformationCircleIcon;
    case 'system':
      return XCircleIcon;
    case 'revenue':
      return CheckCircleIcon;
    default:
      return BellIcon;
  }
};

const getAlertColor = (severity: DashboardAlert['severity']) => {
  switch (severity) {
    case 'critical':
      return 'error';
    case 'high':
      return 'warning';
    case 'medium':
      return 'primary';
    case 'low':
      return 'success';
    default:
      return 'default';
  }
};

const getSeverityLabel = (severity: DashboardAlert['severity']) => {
  switch (severity) {
    case 'critical':
      return 'Critique';
    case 'high':
      return 'Élevé';
    case 'medium':
      return 'Moyen';
    case 'low':
      return 'Faible';
    default:
      return severity;
  }
};

const formatTimeAgo = (timestamp: string) => {
  const now = new Date();
  const alertTime = new Date(timestamp);
  const diffInMinutes = Math.floor((now.getTime() - alertTime.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) {
    return 'À l\'instant';
  } else if (diffInMinutes < 60) {
    return `Il y a ${diffInMinutes} min`;
  } else if (diffInMinutes < 1440) {
    const hours = Math.floor(diffInMinutes / 60);
    return `Il y a ${hours}h`;
  } else {
    const days = Math.floor(diffInMinutes / 1440);
    return `Il y a ${days}j`;
  }
};

/**
 * Composant d'affichage des alertes du dashboard
 */
export const AlertsPanel: React.FC<AlertsPanelProps> = ({ 
  alerts, 
  onMarkAsRead,
  loading = false 
}) => {
  const unreadAlerts = alerts.filter(alert => !alert.isRead);
  const recentAlerts = alerts.slice(0, 5); // Afficher les 5 dernières alertes

  if (loading) {
    return (
      <Card title="Alertes" className="h-96">
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="animate-pulse flex items-start space-x-3">
              <div className="w-8 h-8 bg-neutral-200 rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-neutral-200 rounded w-3/4"></div>
                <div className="h-3 bg-neutral-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </Card>
    );
  }

  return (
    <Card 
      title="Alertes" 
      description={`${unreadAlerts.length} non lues`}
      actions={
        unreadAlerts.length > 0 && (
          <Badge variant="error" size="sm">
            {unreadAlerts.length}
          </Badge>
        )
      }
      className="h-96"
    >
      <div className="space-y-4 max-h-80 overflow-y-auto">
        {recentAlerts.length === 0 ? (
          <div className="text-center py-8">
            <BellIcon className="mx-auto h-12 w-12 text-neutral-400" />
            <h3 className="mt-2 text-sm font-medium text-neutral-900">
              Aucune alerte
            </h3>
            <p className="mt-1 text-sm text-neutral-500">
              Toutes les opérations se déroulent normalement.
            </p>
          </div>
        ) : (
          recentAlerts.map((alert) => {
            const IconComponent = getAlertIcon(alert.type);
            const alertColor = getAlertColor(alert.severity);

            return (
              <div
                key={alert.id}
                className={`flex items-start space-x-3 p-3 rounded-lg transition-colors ${
                  alert.isRead 
                    ? 'bg-neutral-50' 
                    : 'bg-white border border-neutral-200 shadow-sm'
                }`}
              >
                <div className="flex-shrink-0">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    alertColor === 'error' ? 'bg-error-100' :
                    alertColor === 'warning' ? 'bg-warning-100' :
                    alertColor === 'primary' ? 'bg-primary-100' :
                    alertColor === 'success' ? 'bg-success-100' :
                    'bg-neutral-100'
                  }`}>
                    <IconComponent className={`w-4 h-4 ${
                      alertColor === 'error' ? 'text-error-600' :
                      alertColor === 'warning' ? 'text-warning-600' :
                      alertColor === 'primary' ? 'text-primary-600' :
                      alertColor === 'success' ? 'text-success-600' :
                      'text-neutral-600'
                    }`} />
                  </div>
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <p className={`text-sm font-medium ${
                        alert.isRead ? 'text-neutral-600' : 'text-neutral-900'
                      }`}>
                        {alert.title}
                      </p>
                      <p className={`text-sm mt-1 ${
                        alert.isRead ? 'text-neutral-500' : 'text-neutral-600'
                      }`}>
                        {alert.message}
                      </p>
                      <div className="flex items-center space-x-2 mt-2">
                        <Badge 
                          variant={alertColor} 
                          size="sm"
                        >
                          {getSeverityLabel(alert.severity)}
                        </Badge>
                        <span className="text-xs text-neutral-500">
                          {formatTimeAgo(alert.createdAt)}
                        </span>
                      </div>
                    </div>

                    {!alert.isRead && onMarkAsRead && (
                      <button
                        onClick={() => onMarkAsRead(alert.id)}
                        className="ml-2 text-neutral-400 hover:text-neutral-600 transition-colors"
                        title="Marquer comme lu"
                      >
                        <XMarkIcon className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>

      {alerts.length > 5 && (
        <div className="mt-4 pt-4 border-t border-neutral-200">
          <button className="text-sm text-primary-600 hover:text-primary-700 font-medium">
            Voir toutes les alertes ({alerts.length})
          </button>
        </div>
      )}
    </Card>
  );
};
