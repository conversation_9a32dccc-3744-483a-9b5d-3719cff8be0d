import { useState, useEffect, useCallback } from 'react';
import { 
  dashboardService, 
  type DashboardStats, 
  type DashboardAlert, 
  type RecentActivity 
} from '@/services/dashboardService';

interface UseDashboardState {
  stats: DashboardStats | null;
  alerts: DashboardAlert[];
  activities: RecentActivity[];
  loading: boolean;
  error: string | null;
}

interface UseDashboardActions {
  refreshStats: () => Promise<void>;
  refreshAlerts: () => Promise<void>;
  refreshActivities: () => Promise<void>;
  markAlertAsRead: (alertId: string) => Promise<void>;
  refresh: () => Promise<void>;
}

/**
 * Hook pour la gestion des données du dashboard
 */
export function useDashboard(): UseDashboardState & UseDashboardActions {
  const [state, setState] = useState<UseDashboardState>({
    stats: null,
    alerts: [],
    activities: [],
    loading: false,
    error: null,
  });

  /**
   * Récupère les statistiques du dashboard
   */
  const refreshStats = useCallback(async (): Promise<void> => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      const stats = await dashboardService.getDashboardStats();
      setState(prev => ({ ...prev, stats, loading: false }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur lors du chargement des statistiques';
      setState(prev => ({ ...prev, error: errorMessage, loading: false }));
      throw error;
    }
  }, []);

  /**
   * Récupère les alertes du dashboard
   */
  const refreshAlerts = useCallback(async (): Promise<void> => {
    try {
      const alerts = await dashboardService.getDashboardAlerts();
      setState(prev => ({ ...prev, alerts }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur lors du chargement des alertes';
      setState(prev => ({ ...prev, error: errorMessage }));
      throw error;
    }
  }, []);

  /**
   * Récupère les activités récentes
   */
  const refreshActivities = useCallback(async (): Promise<void> => {
    try {
      const activities = await dashboardService.getRecentActivities();
      setState(prev => ({ ...prev, activities }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur lors du chargement des activités';
      setState(prev => ({ ...prev, error: errorMessage }));
      throw error;
    }
  }, []);

  /**
   * Marque une alerte comme lue
   */
  const markAlertAsRead = useCallback(async (alertId: string): Promise<void> => {
    try {
      await dashboardService.markAlertAsRead(alertId);
      setState(prev => ({
        ...prev,
        alerts: prev.alerts.map(alert =>
          alert.id === alertId ? { ...alert, isRead: true } : alert
        ),
      }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur lors de la mise à jour de l\'alerte';
      setState(prev => ({ ...prev, error: errorMessage }));
      throw error;
    }
  }, []);

  /**
   * Rafraîchit toutes les données
   */
  const refresh = useCallback(async (): Promise<void> => {
    await Promise.all([
      refreshStats(),
      refreshAlerts(),
      refreshActivities(),
    ]);
  }, [refreshStats, refreshAlerts, refreshActivities]);

  // Chargement initial
  useEffect(() => {
    refresh();
  }, []);

  // Rafraîchissement automatique toutes les 5 minutes
  useEffect(() => {
    const interval = setInterval(() => {
      refresh();
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [refresh]);

  return {
    ...state,
    refreshStats,
    refreshAlerts,
    refreshActivities,
    markAlertAsRead,
    refresh,
  };
}

/**
 * Hook pour les données de graphiques
 */
export function useChartData(type: 'revenue' | 'trips' | 'bookings', period: 'week' | 'month' | 'year') {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const chartData = await dashboardService.getChartData(type, period);
      setData(chartData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors du chargement des données';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [type, period]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    refresh: fetchData,
  };
}
